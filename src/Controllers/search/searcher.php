<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$search = $_POST["query"];

if ($search === "") { ?>
    <section class="bg-white h-full dark:bg-gray-900">
        <div class="py-8 px-4 h-full mx-auto max-w-screen-xl flex flex-col items-center justify-center text-center lg:py-16 lg:px-12">
            <svg xmlns="http://www.w3.org/2000/svg" width="96" height="96" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-scan-search-icon dark:text-gray-100 lucide-scan-search">
                <path d="M3 7V5a2 2 0 0 1 2-2h2" />
                <path d="M17 3h2a2 2 0 0 1 2 2v2" />
                <path d="M21 17v2a2 2 0 0 1-2 2h-2" />
                <path d="M7 21H5a2 2 0 0 1-2-2v-2" />
                <circle cx="12" cy="12" r="3" />
                <path d="m16 16-1.9-1.9" />
            </svg>
            <h1
                class="mt-8 mb-4 text-4xl font-extrabold tracking-tight leading-none text-gray-900 md:text-5xl lg:text-6xl dark:text-white">
                Vyhľadávanie</h1>
            <p class="mb-8 text-lg font-normal text-gray-500 lg:text-xl sm:px-16 xl:px-48 dark:text-gray-400">Pre zobrazenie výsledkov napíšte do
                vyhľadávania čo chcete vyhľadať</p>
        </div>
    </section>
    <?php exit;
}

$fts_query = preg_replace('/\s+/', ' & ', $search);
$like_query = '%' . addcslashes($search, '%_') . '%';

$sql = "SELECT type, id, title,
       ts_rank_cd(searchable, query) AS rank
FROM global_search, to_tsquery('simple', '$fts_query') query
WHERE searchable @@ query
   OR title ILIKE '$like_query'
ORDER BY rank DESC
LIMIT 20";

echo $sql;

$results = Connection::getDataFromDatabase($sql, defaultDB)[1];
print_r($results);
