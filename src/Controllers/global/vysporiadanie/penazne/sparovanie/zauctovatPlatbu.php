<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/src/constants/global/globalCons.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/Payments/TransactionMatcher.class.php";
require_once "/home/<USER>/www/src/lib/Payments/Sparovanie/PaymentCharge.class.php";
require_once "/home/<USER>/www/src/lib/Payments/Sparovanie/PaymentChargerFunctions.class.php";
require_once('/home/<USER>/www/src/lib/Payments/Exceptions/PaymentProcessingException.php');

$obratID = $_POST["uhradaid"];
$kodobratu = $_POST["kodobratu"];

$payment = [
    "id" => $obratID,
    "tranza" => $_POST["tranza"],
    "destinacia" => $_POST["destinacia"],
    "datum" => $_POST["datum"],
    "mena" => $_POST["mena"],
    "cub" => $_POST["cub"],
    "suma" => $_POST["suma"],
    "vs" => $_POST["vs"],
    "ss" => $_POST["ss"],
    "ks" => $_POST["ks"],
    "ucetpartnera" => $_POST["ucetpartnera"],
    "nazovpartnera" => $_POST["nazovpartnera"],
    "forma" => $_POST["forma"],
    "dealid" => $_POST["dealid"],
    "logactivityid" => $_POST["logactivityid"],
    "logdatatimeactivity" => $_POST["logdatatimeactivity"],
    "obratid" => $obratID
];
echo "KODOBRATU: $kodobratu";
try {
    $paymentCharger = new PaymentCharger($kodobratu, $payment);
    switch ($kodobratu) {
        case '201':
            //Vklad penaznych prostriedkov
            $paymentCharger->paymentCharge201();
            break;
        case '202':
            //Vratenie -> nesplnenie limitu
            //TODO zauctuj_platbu_202
            break;
        case '237': //konverzia
            $paymentCharger->paymentCharge237();
            break;
        case '203': //splatna istina tv
        case '204': //splatny urok tv
        case '206': //splatny nominal dlh
        case '209': //splatny kupon dlh
        case '210': //splatny urok bu
        case '211': //vklad podielnika presun
        case '216': //vynosy z ds
        case '221': //vstupny poplatok
        case '222': //prestupny poplatok
        case '223': //vystupny poplatok
        case '224': //odplata spravcu
        case '225': //odplata depozitara
        case '245': //forwardova konverzia
        case '260': //forwardova konverzia
        case '261': //forwardova konverzia
        case '262': //forwardova konverzia
        case '263': //forwardova konverzia
        case '264': //forwardova konverzia
        case '265': //forwardova konverzia
        case '266': //poplatok dobropis
            $paymentCharger->paymentCharge203to266();
            break;
        case '205': //predany dlhopis
            $paymentCharger->paymentCharge205();
            break;
        case '208': //splatny dlhopis -> nominal + kupon / 206 + 209
            $paymentCharger->paymentCharge208To206();
            //TODO zauctuj_platbu_208
            break;

        case '217': //splatna pohladavka
            //TODO zauctuj_platbu_217
            break;

        case '214': //financne vynosy
        case '226': //vlastne vykony
        case '227': //prevadzkove vykovy
        case '228': //vklad akcionara
        case '229': //mimoriadny vynos
        case '238': //dividenda z akcie
            //TODO zauctuj_platbu_214
            break;

        case '231': //predana akcia
        case '233': //predany podielovy fond
            $paymentCharger->predanaAkcia();
            break;

        case '235': //splatny nominal dep cert
        case '236': //splatny urok dep cert
            //TODO zauctuj_platbu_235
            break;
        case '249': //nespecifikovana pohladavka
            //TODO zauctuj_platbu203.php
            break;
        case '284': //splatenie akcie
            $paymentCharger->paymentCharge284();
            break;
        case '219':
            //tu sa uctuje iba 219 (nesparovatelna platba)
            $paymentCharger->paymentCharge219();
            break;
    }
    ?>
    <div id="toast-success"
        class="flex items-center w-full max-w-xs p-4 mb-4 text-gray-500 bg-white rounded-lg shadow-sm dark:text-gray-400 dark:bg-gray-600"
        role="alert">
        <div
            class="inline-flex items-center justify-center shrink-0 w-8 h-8 text-green-500 bg-green-100 rounded-lg dark:bg-green-800 dark:text-green-200">
            <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                viewBox="0 0 20 20">
                <path
                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z" />
            </svg>
            <span class="sr-only">Check icon</span>
        </div>
        <div class="ms-3 text-sm font-normal">Úhrada č. <strong><?php echo $results["paymentId"]; ?></strong> bola úspešne
            spracovaná so statusom <strong><?php echo $results["status"]; ?></strong></div>
        <button type="button"
            class="ms-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
            data-dismiss-target="#toast-success" aria-label="Close">
            <span class="sr-only">Close</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
        </button>
    </div>
    <script>
        setTimeout(() => {
            htmx.ajax('GET', window.location.pathname + window.location.search, {
                target: "#pageContentMain",
            });
        }, 1500);
    </script>
    <?php
} catch (\Exception $e) {
    error_log("Unexpected error: " . $e->getMessage()); ?>
    <div id="toast-danger"
        class="flex items-start w-full max-w-lg p-4 mb-4 text-gray-500 bg-white rounded-lg shadow-sm dark:text-gray-400 dark:bg-gray-600"
        role="alert">
        <div
            class="inline-flex items-center justify-center shrink-0 w-8 h-8 text-red-500 bg-red-100 rounded-lg dark:bg-red-800 dark:text-red-200">
            <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                viewBox="0 0 20 20">
                <path
                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 11.793a1 1 0 1 1-1.414 1.414L10 11.414l-2.293 2.293a1 1 0 0 1-1.414-1.414L8.586 10 6.293 7.707a1 1 0 0 1 1.414-1.414L10 8.586l2.293-2.293a1 1 0 0 1 1.414 1.414L11.414 10l2.293 2.293Z" />
            </svg>
            <span class="sr-only">Error icon</span>
        </div>
        <div class="ms-3 text-sm font-normal"><strong>Úhradu sa nepodarilo kvôli tejto chybe</strong>:
            <br><?php echo $e->getMessage(); ?>
        </div>
        <button type="button"
            class="ms-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
            data-dismiss-target="#toast-danger" aria-label="Close">
            <span class="sr-only">Close</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
        </button>
    </div>
    <script>
        $(".zapocetSpin").css("display", "none");
        $(".spanek").html("Spárovať platbu");
    </script>
    <?php
}