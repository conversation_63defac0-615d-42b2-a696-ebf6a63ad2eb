<?php
session_start();
require_once("/home/<USER>/www/conf/settings.php");
require_once("/home/<USER>/www/src/lib/connection.php");

$userID = $_SESSION["user"]["data"]["userid"];
$date = new DateTime("now", new DateTimeZone('Europe/Bratislava'));
$today = $date->format('Y-m-d H:i:s T');
$checkLoginStatus = Connection::getDataFromDatabase("SELECT logged, forcelogout FROM users WHERE userid = $userID", defaultDB);

if (!$checkLoginStatus[1][0]["logged"]) {
    unset($_SESSION['user']);
    session_destroy();
    header("Location: /");
}

if ($checkLoginStatus[1][0]["forcelogout"]) {
    unset($_SESSION['user']);
    session_destroy();
    header("Location: /?logoutKilled=1");
}

if (!isset($content)) {
    $content = '<p>There is nothing to show</p>';
}
?>
<html lang="sk">
<?php include_once "head.php";
?>

<body hx-ext="preload">
    <div class="antialiased bg-gray-50 dark:bg-slate-500">
        <div class="flex" id="appWrapper">
            <p><?php print_r($path); ?></p>
            <section id="drawer-navigation-wrapper">
                <?php echo $menu; ?>
            </section>
            <section id="notificationDropdownOverlay"
                style="position: fixed; width: 0; height: 0;opacity: 0;background: #00000052; overflow-x: hidden;">
                <?php include "/home/<USER>/www/src/Components/layout/header/notificationsDropdown.php"; ?>
            </section>
            <section id="profileOverlay"
                style="position: fixed; width: 0; height: 0;opacity: 0;background: #00000052; overflow-x: hidden;">
                <?php include "/home/<USER>/www/src/Components/layout/header/userDropDown.php"; ?>
            </section>
            <main id="mainElement" class="w-full bg-white dark:bg-gray-800"
                style="padding-left: 15rem; min-height: 100vh;border: none;">
                <div class="h-full relative dark:bg-gray-800 bg-white"
                    style="border-radius: 1.5rem 0 0 1.5rem; box-shadow: -7px 0px 37px 11px rgba(0,0,0,0.29);-webkit-box-shadow: 5px 0px 35px 0px rgba(0,0,0,0.29);-moz-box-shadow: -7px 0px 37px 11px rgba(0,0,0,0.29);">
                    <nav id="topbar-wrapper"
                        class="px-4 dark:bg-gray-600 py-2 sticky z-30 shadow-md rounded-t-md top-0 bg-white">
                        <?php include "/home/<USER>/www/src/Components/layout/header/topBar.php"; ?>
                    </nav>
                    <section id="appBreadCrumb">
                        <?php echo $breadcrumb; ?>
                    </section>
                    <div class="globalSearchResults bg-white dark:bg-gray-900 absolute shadow-lg rounded-lg z-20"
                        style="height: 0; top: 7vh; left: 0; margin: 0; width: 100%;"></div>
                    <section id="pageContentMain" class="relative">
                        <?php echo $content; ?>
                    </section>
                </div>
            </main>
        </div>
    </div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.3.0/flowbite.min.js"></script>
    <script src="/src/assets/js/index.js"></script>
    <script src="/src/assets/js/layout/themeToggle.js"></script>
    <script src="/src/assets/js/layout/search/topBarSearch.js"></script>
</body>

</html>