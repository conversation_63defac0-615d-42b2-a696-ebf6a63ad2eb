function enableSearch() {
    $("#topbar-searchWrapper").animate({ scale: 1.05 }, 100);
    $(".globalSearchResults").animate({ height: '93vh' }, 300);
    document.querySelector(".globalSearchResults").style.overflow = "auto";
}

function openNotifications(e) {
    e.stopPropagation();
    $("#notification-dropdown").animate({ right: "1rem" }, 200);
    $("#notificationDropdownOverlay").css("z-index", "40");
    $("#notificationDropdownOverlay").css("width", "100%");
    $("#notificationDropdownOverlay").css("height", "100vh");
    $("#notificationDropdownOverlay").animate({ opacity: 1 }, 200);
}

function openUserMenu(e) {
    e.stopPropagation();
    $("#usersidebar-dropdown").animate({ right: "1rem" }, 200);
    $("#profileOverlay").css("z-index", "40");
    $("#profileOverlay").css("width", "100%");
    $("#profileOverlay").css("height", "100vh");
    $("#profileOverlay").animate({ opacity: 1 }, 200);
}

$("#usersidebar-dropdown").on("click", (e) => {
    if (e.target.id !== "closeUserDropdown") {
        e.stopPropagation();
    }
});

$("#notification-dropdown").on("click", (e) => {
    console.log(e.target);
    if (e.target.id !== "closeNotificationDropdown" || e.target.id !== "markReadBtn") {
        e.stopPropagation();
    }
    if (e.target.id !== "closeUserDropdown") {
        e.stopPropagation();
    }
});

$("#closeUserDropdown").on("click", (e) => {
    $("#notification-dropdown").animate({ right: "-25rem" }, 200);
    $("#profileOverlay").animate({ opacity: 0 }, 100);
    setTimeout(() => {
        $("#profileOverlay").css("z-index", "0");
        $("#profileOverlay").css("width", "0");
        $("#profileOverlay").css("height", "0");
    }, 300);
});

$("#closeNotificationDropdown").on("click", (e) => {
    $("#notification-dropdown").animate({ right: "-25rem" }, 200);
    $("#notificationDropdownOverlay").animate({ opacity: 0 }, 100);
    setTimeout(() => {
        $("#notificationDropdownOverlay").css("z-index", "0");
        $("#notificationDropdownOverlay").css("width", "0");
        $("#notificationDropdownOverlay").css("height", "0");
    }, 300);
});

// Consolidated document click handler to avoid conflicts
$(document).click(function (event) {
    // Handle search results
    let searchResults = $(".globalSearchResults");
    let searchInput = $("#topbar-search");
    if (!searchResults.is(event.target) && !searchResults.has(event.target).length &&
        !searchInput.is(event.target) && !searchInput.has(event.target).length) {
        document.querySelector(".globalSearchResults").style.overflow = "hidden";
        $("#topbar-searchWrapper").animate({ scale: 1 }, 100);
        $(".globalSearchResults").animate({ height: '0' }, 300);
    }

    // Handle notification dropdown
    let obj = $(".notification-dropdown");
    if (!obj.is(event.target) && !obj.has(event.target).length) {
        $("#notification-dropdown").animate({ right: "-25rem" }, 200);
        $("#notificationDropdownOverlay").animate({ opacity: 0 }, 100);
        setTimeout(() => {
            $("#notificationDropdownOverlay").css("z-index", "0");
            $("#notificationDropdownOverlay").css("width", "0");
            $("#notificationDropdownOverlay").css("height", "0");
        }, 300);
    }

    // Handle user sidebar dropdown
    let usr = $("#usersidebar-dropdown");
    if (!usr.is(event.target) && !usr.has(event.target).length) {
        $("#usersidebar-dropdown").animate({ right: "-25rem" }, 200);
        $("#profileOverlay").animate({ opacity: 0 }, 100);
        setTimeout(() => {
            $("#profileOverlay").css("z-index", "0");
            $("#profileOverlay").css("width", "0");
            $("#profileOverlay").css("height", "0");
        }, 300);
    }
});

$("#markReadBtn").on("click", (e) => {
    $("#markReadIcon").css("display", "none");
    $("#markReadSpinner").css("display", "block");
})

document.body.addEventListener('htmx:afterSettle', function (evt) {
    $("#markReadIcon").css("display", "block");
    $("#markReadSpinner").css("display", "none");
    const notifCount = $("#notifCountHiddenAfter").html();
    console.log(notifCount);
    $("#notification-dot").html(notifCount);
    $("#notifCount").html(notifCount);
    if (parseInt(notifCount) === 0) {
        $("#notification-dot").css("display", "none");
    }
});

// New coordinating function that ensures proper sequencing
async function switchModeAndRefresh(e, preserveUrl) {
    try {
        // First, switch the mode and wait for it to complete
        await appModeSwitcher(e, preserveUrl);

        // Then refresh all UI components in sequence
        await refreshMenu(e);
        await refreshTopBar(e);
        await refreshBreadcrumb(e);

        console.log('Mode switch and UI refresh completed successfully');
    } catch (error) {
        console.error('Error during mode switch:', error);
    }
}

async function refreshMenu(e) {
    e.stopPropagation();
    return htmx.ajax('POST', '/refreshMenu', {
        target: "#drawer-navigation-wrapper",
        values: {
            path: e.currentTarget ? e.currentTarget.attributes[1].nodeValue : "/"
        },
    });
}

async function refreshTopBar(e) {
    return htmx.ajax('POST', '/refreshTopBar', {
        target: "#topbar-wrapper",
        source: "#topbar-wrapper",
        values: {
            path: e.currentTarget ? e.currentTarget.attributes[1].nodeValue : "/"
        },
    });
}

async function refreshBreadcrumb(e) {
    e.stopPropagation();
    return htmx.ajax('POST', '/breadcrumb', {
        target: "#appBreadCrumb",
        source: "#appBreadCrumb",
        values: {
            path: e.currentTarget ? e.currentTarget.attributes[1].nodeValue : "/"
        },
    });
}

async function refreshMenuHome() {
    return htmx.ajax('POST', '/refreshMenu', {
        target: "#drawer-navigation-wrapper",
        source: "#drawer-navigation-wrapper",
        values: {
            path: "/"
        },
    });
}

async function refreshAllUIComponents(e) {
    try {
        await refreshMenu(e);
        await refreshTopBar(e);
        await refreshBreadcrumb(e);
        console.log('UI components refreshed successfully');
    } catch (error) {
        console.error('Error refreshing UI components:', error);
    }
}

(function () {
    let typingTimer;
    const doneTyping = 500;

    // $("#resetSearchCPList").on("click", () => {
    //     $("#searchBarCPList").val("");
    //     document.getElementById("resetSearchCPList").style.display = "none";
    //     document.getElementById("loadingSearchCPList").style.display = "none";
    //     $("#submitFilterFormCP").click();
    // });

    $("#topbar-search").on("keyup", (e) => {
        clearTimeout(typingTimer);
        typingTimer = setTimeout(() => {
            htmx.ajax('POST', `/search`,
                {
                    target: ".globalSearchResults",
                    values: {
                        "query": e.target.value
                    }
                }).then((response) => {

                });
        }, doneTyping);

    });

    $("#topbar-search").on("keydown", () => {
        clearTimeout(typingTimer);
    });
})();